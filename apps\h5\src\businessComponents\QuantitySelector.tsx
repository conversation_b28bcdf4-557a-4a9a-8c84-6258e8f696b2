import { Stepper } from 'antd-mobile'

interface QuantitySelectorProps {
  value?: number
  limitCount?: number | null
  inputMax?: number
  onQtyChange: (value: number, max: number) => void
  disabled?: boolean
  style?: object
}

/**
 * 商品数量选择器组件
 * @param limitCount - 限购数量
 * @param inputMax - 最大可购买数量
 * @param onQtyChange - 数量变化回调
 * @param disabled - 是否禁用
 * @param style - 自定义样式
 */
const QuantitySelector = ({
  value,
  limitCount,
  inputMax = 99,
  onQtyChange,
  disabled = false,
  style,
}: QuantitySelectorProps) => {
  /**
   * 处理数量变化
   * 当当前数量超过最大值时，减号按钮应该直接设置为最大值
   */
  const handleQuantityChange = (newValue: number) => {
    // 如果是减少操作且当前值超过最大值，直接设置为最大值
    if (value && newValue < value && value > inputMax) {
      onQtyChange(inputMax, inputMax)
    } else {
      onQtyChange(newValue, inputMax)
    }
  }

  return (
    <div className="flex items-center gap-base">
      {limitCount && <span className="text-sm text-gray-500">最多购买{limitCount}件</span>}
      <Stepper
        value={value}
        disabled={disabled}
        digits={0}
        defaultValue={1}
        className="custom-stepper"
        style={{
          '--border': '1px solid #f3f3f4',
          '--border-radius': '999px',
          '--height': '3rem',
          '--input-width': '4rem',
          '--input-background-color': '#fff',
          ...style,
        }}
        onBlur={handleQuantityChange}
        min={1}
        max={inputMax}
      />
    </div>
  )
}

export default QuantitySelector
